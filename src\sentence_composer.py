"""
Sentence Composition System for SIBI Sign Language Detection
"""

import time
import logging
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass, field
from collections import deque
import json

from src.detection_engine import Detection, DetectionResult
from config.settings import DETECTION_CONFIG, SIBI_CLASSES

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class WordEntry:
    """Data class for a word in the sentence"""
    word: str
    confidence: float
    timestamp: float
    detection_count: int = 1
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert word entry to dictionary"""
        return {
            "word": self.word,
            "confidence": self.confidence,
            "timestamp": self.timestamp,
            "detection_count": self.detection_count
        }

@dataclass
class Sentence:
    """Data class for a composed sentence"""
    words: List[WordEntry] = field(default_factory=list)
    created_at: float = field(default_factory=time.time)
    last_updated: float = field(default_factory=time.time)
    is_complete: bool = False
    
    def get_text(self) -> str:
        """Get sentence as text string"""
        return " ".join([word.word for word in self.words])
    
    def get_word_count(self) -> int:
        """Get number of words in sentence"""
        return len(self.words)
    
    def get_avg_confidence(self) -> float:
        """Get average confidence of all words"""
        if not self.words:
            return 0.0
        return sum(word.confidence for word in self.words) / len(self.words)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert sentence to dictionary"""
        return {
            "text": self.get_text(),
            "words": [word.to_dict() for word in self.words],
            "word_count": self.get_word_count(),
            "avg_confidence": self.get_avg_confidence(),
            "created_at": self.created_at,
            "last_updated": self.last_updated,
            "is_complete": self.is_complete
        }

class SentenceComposer:
    """
    System for composing sentences from detected sign language words
    """
    
    def __init__(self):
        """Initialize the sentence composer"""
        self.max_sentence_length = DETECTION_CONFIG["max_sentence_length"]
        self.auto_clear_timeout = DETECTION_CONFIG["auto_clear_timeout"]
        self.word_confirmation_threshold = 2  # Number of detections to confirm a word
        self.min_word_confidence = 0.4  # Minimum confidence for word acceptance
        
        # Current sentence being composed
        self.current_sentence = Sentence()
        
        # Sentence history
        self.sentence_history = deque(maxlen=50)  # Keep last 50 sentences
        
        # Word detection tracking
        self.pending_words = {}  # word -> list of detections
        self.last_word_time = 0
        self.word_spacing_threshold = 1.0  # Minimum seconds between words
        
        # Statistics
        self.total_words_detected = 0
        self.total_sentences_composed = 0
        self.word_frequency = {}
        
        logger.info("Sentence composer initialized")
    
    def process_detection_result(self, result: DetectionResult) -> bool:
        """
        Process detection result and update sentence composition
        
        Args:
            result: Detection result from the detection engine
            
        Returns:
            bool: True if sentence was updated, False otherwise
        """
        if not result.detections:
            self._check_auto_clear(result.frame_timestamp)
            return False
        
        # Get the best detection (highest confidence)
        best_detection = result.get_best_detection()
        if not best_detection:
            return False
        
        # Process the detection
        word_added = self._process_word_detection(best_detection)
        
        # Check for auto-clear timeout
        self._check_auto_clear(result.frame_timestamp)
        
        return word_added
    
    def _process_word_detection(self, detection: Detection) -> bool:
        """
        Process a single word detection
        
        Args:
            detection: Detected word
            
        Returns:
            bool: True if word was added to sentence, False otherwise
        """
        word = detection.class_name
        confidence = detection.confidence
        timestamp = detection.timestamp
        
        # Check minimum confidence
        if confidence < self.min_word_confidence:
            return False
        
        # Check word spacing (prevent rapid repeated detections)
        if timestamp - self.last_word_time < self.word_spacing_threshold:
            return False
        
        # Add to pending words for confirmation
        if word not in self.pending_words:
            self.pending_words[word] = []
        
        self.pending_words[word].append({
            "confidence": confidence,
            "timestamp": timestamp
        })
        
        # Check if word has enough confirmations
        if len(self.pending_words[word]) >= self.word_confirmation_threshold:
            return self._confirm_word(word)
        
        return False
    
    def _confirm_word(self, word: str) -> bool:
        """
        Confirm and add a word to the current sentence
        
        Args:
            word: Word to confirm and add
            
        Returns:
            bool: True if word was added successfully, False otherwise
        """
        if word not in self.pending_words:
            return False
        
        detections = self.pending_words[word]
        
        # Calculate average confidence
        avg_confidence = sum(d["confidence"] for d in detections) / len(detections)
        latest_timestamp = max(d["timestamp"] for d in detections)
        
        # Check sentence length limit
        if len(self.current_sentence.words) >= self.max_sentence_length:
            logger.warning(f"Sentence length limit reached ({self.max_sentence_length})")
            return False
        
        # Create word entry
        word_entry = WordEntry(
            word=word,
            confidence=avg_confidence,
            timestamp=latest_timestamp,
            detection_count=len(detections)
        )
        
        # Add word to current sentence
        self.current_sentence.words.append(word_entry)
        self.current_sentence.last_updated = latest_timestamp
        self.last_word_time = latest_timestamp
        
        # Update statistics
        self.total_words_detected += 1
        self.word_frequency[word] = self.word_frequency.get(word, 0) + 1
        
        # Clear pending detections for this word
        del self.pending_words[word]
        
        logger.info(f"Added word '{word}' to sentence (confidence: {avg_confidence:.2f})")
        return True
    
    def _check_auto_clear(self, current_time: float):
        """
        Check if sentence should be auto-cleared due to timeout
        
        Args:
            current_time: Current timestamp
        """
        if not self.current_sentence.words:
            return
        
        time_since_last_word = current_time - self.current_sentence.last_updated
        if time_since_last_word >= self.auto_clear_timeout:
            self.complete_sentence()
            logger.info("Auto-cleared sentence due to timeout")
    
    def add_word_manually(self, word: str, confidence: float = 1.0) -> bool:
        """
        Manually add a word to the current sentence
        
        Args:
            word: Word to add
            confidence: Confidence score (default: 1.0)
            
        Returns:
            bool: True if word was added successfully, False otherwise
        """
        if word not in SIBI_CLASSES.values():
            logger.warning(f"Unknown word: {word}")
            return False
        
        if len(self.current_sentence.words) >= self.max_sentence_length:
            logger.warning(f"Sentence length limit reached ({self.max_sentence_length})")
            return False
        
        word_entry = WordEntry(
            word=word,
            confidence=confidence,
            timestamp=time.time(),
            detection_count=1
        )
        
        self.current_sentence.words.append(word_entry)
        self.current_sentence.last_updated = time.time()
        
        # Update statistics
        self.total_words_detected += 1
        self.word_frequency[word] = self.word_frequency.get(word, 0) + 1
        
        logger.info(f"Manually added word '{word}' to sentence")
        return True
    
    def remove_last_word(self) -> bool:
        """
        Remove the last word from the current sentence
        
        Returns:
            bool: True if word was removed, False if sentence is empty
        """
        if not self.current_sentence.words:
            return False
        
        removed_word = self.current_sentence.words.pop()
        self.current_sentence.last_updated = time.time()
        
        logger.info(f"Removed word '{removed_word.word}' from sentence")
        return True
    
    def clear_current_sentence(self):
        """Clear the current sentence without saving to history"""
        self.current_sentence = Sentence()
        self.pending_words.clear()
        logger.info("Cleared current sentence")
    
    def complete_sentence(self) -> Optional[Sentence]:
        """
        Complete the current sentence and save to history
        
        Returns:
            Completed sentence or None if sentence is empty
        """
        if not self.current_sentence.words:
            return None
        
        # Mark sentence as complete
        self.current_sentence.is_complete = True
        completed_sentence = self.current_sentence
        
        # Add to history
        self.sentence_history.append(completed_sentence)
        self.total_sentences_composed += 1
        
        # Start new sentence
        self.current_sentence = Sentence()
        self.pending_words.clear()
        
        logger.info(f"Completed sentence: '{completed_sentence.get_text()}'")
        return completed_sentence
    
    def get_current_sentence(self) -> Sentence:
        """Get the current sentence being composed"""
        return self.current_sentence
    
    def get_sentence_history(self, limit: int = 10) -> List[Sentence]:
        """
        Get recent sentence history
        
        Args:
            limit: Maximum number of sentences to return
            
        Returns:
            List of recent sentences
        """
        return list(self.sentence_history)[-limit:]
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        Get composition statistics
        
        Returns:
            Dictionary with statistics
        """
        return {
            "total_words_detected": self.total_words_detected,
            "total_sentences_composed": self.total_sentences_composed,
            "current_sentence_length": len(self.current_sentence.words),
            "pending_words": len(self.pending_words),
            "word_frequency": dict(self.word_frequency),
            "avg_words_per_sentence": (
                self.total_words_detected / self.total_sentences_composed
                if self.total_sentences_composed > 0 else 0
            ),
            "sentence_history_count": len(self.sentence_history)
        }
    
    def export_sentences(self, format: str = "json") -> str:
        """
        Export sentence history
        
        Args:
            format: Export format ("json" or "text")
            
        Returns:
            Exported data as string
        """
        if format == "json":
            data = {
                "current_sentence": self.current_sentence.to_dict(),
                "sentence_history": [s.to_dict() for s in self.sentence_history],
                "statistics": self.get_statistics()
            }
            return json.dumps(data, indent=2)
        
        elif format == "text":
            lines = []
            lines.append("=== SIBI Sentence Composition Export ===\n")
            
            if self.current_sentence.words:
                lines.append(f"Current Sentence: {self.current_sentence.get_text()}\n")
            
            lines.append("Sentence History:")
            for i, sentence in enumerate(self.sentence_history, 1):
                lines.append(f"{i}. {sentence.get_text()}")
            
            return "\n".join(lines)
        
        else:
            raise ValueError(f"Unsupported export format: {format}")
    
    def update_config(self, **kwargs):
        """
        Update composer configuration
        
        Args:
            **kwargs: Configuration parameters to update
        """
        if "max_sentence_length" in kwargs:
            self.max_sentence_length = kwargs["max_sentence_length"]
        if "auto_clear_timeout" in kwargs:
            self.auto_clear_timeout = kwargs["auto_clear_timeout"]
        if "word_confirmation_threshold" in kwargs:
            self.word_confirmation_threshold = kwargs["word_confirmation_threshold"]
        if "min_word_confidence" in kwargs:
            self.min_word_confidence = kwargs["min_word_confidence"]
        if "word_spacing_threshold" in kwargs:
            self.word_spacing_threshold = kwargs["word_spacing_threshold"]
        
        logger.info("Updated sentence composer configuration")

    def get_config(self) -> Dict[str, Any]:
        """
        Get current composer configuration

        Returns:
            Dictionary with current configuration
        """
        return {
            "max_sentence_length": self.max_sentence_length,
            "auto_clear_timeout": self.auto_clear_timeout,
            "word_confirmation_threshold": self.word_confirmation_threshold,
            "min_word_confidence": self.min_word_confidence,
            "word_spacing_threshold": self.word_spacing_threshold
        }
