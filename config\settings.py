"""
Configuration settings for SIBI Sign Language Detection Application
"""

import os
from pathlib import Path
from typing import List, Dict, Any

# Project paths
PROJECT_ROOT = Path(__file__).parent.parent
MODEL_PATH = PROJECT_ROOT / "models" / "sibiv3.pt"
SRC_PATH = PROJECT_ROOT / "src"
UTILS_PATH = PROJECT_ROOT / "utils"

# Model configuration
MODEL_CONFIG = {
    "confidence_threshold": 0.5,
    "iou_threshold": 0.45,
    "max_detections": 10,
    "device": "auto",  # "auto", "cpu", "cuda", "mps"
    "half_precision": True,  # Use FP16 for faster inference
}

# Camera configuration
CAMERA_CONFIG = {
    "width": 640,
    "height": 480,
    "fps": 30,
    "camera_index": 0,
    "flip_horizontal": True,  # Mirror the camera feed
}

# Detection configuration
DETECTION_CONFIG = {
    "min_confidence": 0.3,
    "detection_cooldown": 1.0,  # Seconds between same word detections
    "max_sentence_length": 20,  # Maximum words in a sentence
    "auto_clear_timeout": 30.0,  # Auto clear sentence after N seconds of inactivity
}

# SIBI word classes (must match model training)
SIBI_CLASSES = {
    0: "mau",      # want
    1: "saya",     # I/me
    2: "mana",     # where
    3: "makan",    # eat
    4: "kamu",     # you
    5: "jalan",    # walk/road
    6: "hotel",    # hotel
    7: "ke",       # to
    8: "di",       # at/in
}

# UI configuration
UI_CONFIG = {
    "page_title": "SIBI Sign Language Detection",
    "page_icon": "🤟",
    "layout": "wide",
    "sidebar_width": 300,
    "video_container_height": 500,
}

# Color scheme for UI
COLORS = {
    "primary": "#1f77b4",
    "secondary": "#ff7f0e",
    "success": "#2ca02c",
    "warning": "#d62728",
    "info": "#17becf",
    "background": "#f8f9fa",
}

# Performance settings
PERFORMANCE_CONFIG = {
    "frame_skip": 1,  # Process every Nth frame
    "buffer_size": 5,  # Number of frames to buffer
    "gpu_memory_fraction": 0.8,  # Fraction of GPU memory to use
}

def get_device_info() -> Dict[str, Any]:
    """Get information about available compute devices"""
    import torch
    
    device_info = {
        "cpu_available": True,
        "cuda_available": torch.cuda.is_available(),
        "mps_available": torch.backends.mps.is_available() if hasattr(torch.backends, 'mps') else False,
        "cuda_device_count": torch.cuda.device_count() if torch.cuda.is_available() else 0,
    }
    
    if device_info["cuda_available"]:
        device_info["cuda_device_name"] = torch.cuda.get_device_name(0)
        device_info["cuda_memory"] = torch.cuda.get_device_properties(0).total_memory
    
    return device_info

def get_optimal_device() -> str:
    """Determine the optimal device for model inference"""
    device_info = get_device_info()
    
    if MODEL_CONFIG["device"] != "auto":
        return MODEL_CONFIG["device"]
    
    if device_info["cuda_available"]:
        return "cuda"
    elif device_info["mps_available"]:
        return "mps"
    else:
        return "cpu"

# Environment variables
def load_env_config():
    """Load configuration from environment variables"""
    from dotenv import load_dotenv
    load_dotenv()
    
    # Override defaults with environment variables if present
    if os.getenv("SIBI_CONFIDENCE_THRESHOLD"):
        MODEL_CONFIG["confidence_threshold"] = float(os.getenv("SIBI_CONFIDENCE_THRESHOLD"))
    
    if os.getenv("SIBI_CAMERA_WIDTH"):
        CAMERA_CONFIG["width"] = int(os.getenv("SIBI_CAMERA_WIDTH"))
    
    if os.getenv("SIBI_CAMERA_HEIGHT"):
        CAMERA_CONFIG["height"] = int(os.getenv("SIBI_CAMERA_HEIGHT"))
    
    if os.getenv("SIBI_DEVICE"):
        MODEL_CONFIG["device"] = os.getenv("SIBI_DEVICE")

# Initialize configuration
load_env_config()
