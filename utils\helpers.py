"""
Utility functions for SIBI Sign Language Detection Application
"""

import cv2
import numpy as np
import time
import logging
from typing import List, Dict, Any, Optional, Tuple
import streamlit as st
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def format_time_duration(seconds: float) -> str:
    """
    Format time duration in a human-readable format
    
    Args:
        seconds: Duration in seconds
        
    Returns:
        Formatted time string
    """
    if seconds < 60:
        return f"{seconds:.1f}s"
    elif seconds < 3600:
        minutes = int(seconds // 60)
        secs = seconds % 60
        return f"{minutes}m {secs:.1f}s"
    else:
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = seconds % 60
        return f"{hours}h {minutes}m {secs:.1f}s"

def calculate_fps(frame_times: List[float]) -> float:
    """
    Calculate FPS from a list of frame timestamps
    
    Args:
        frame_times: List of frame timestamps
        
    Returns:
        Calculated FPS
    """
    if len(frame_times) < 2:
        return 0.0
    
    time_diffs = [frame_times[i] - frame_times[i-1] for i in range(1, len(frame_times))]
    avg_time_diff = sum(time_diffs) / len(time_diffs)
    
    return 1.0 / avg_time_diff if avg_time_diff > 0 else 0.0

def resize_frame_maintain_aspect(frame: np.ndarray, target_width: int, target_height: int) -> np.ndarray:
    """
    Resize frame while maintaining aspect ratio
    
    Args:
        frame: Input frame
        target_width: Target width
        target_height: Target height
        
    Returns:
        Resized frame
    """
    h, w = frame.shape[:2]
    
    # Calculate scaling factor
    scale_w = target_width / w
    scale_h = target_height / h
    scale = min(scale_w, scale_h)
    
    # Calculate new dimensions
    new_w = int(w * scale)
    new_h = int(h * scale)
    
    # Resize frame
    resized = cv2.resize(frame, (new_w, new_h))
    
    # Create canvas with target dimensions
    canvas = np.zeros((target_height, target_width, 3), dtype=np.uint8)
    
    # Calculate position to center the resized frame
    y_offset = (target_height - new_h) // 2
    x_offset = (target_width - new_w) // 2
    
    # Place resized frame on canvas
    canvas[y_offset:y_offset+new_h, x_offset:x_offset+new_w] = resized
    
    return canvas

def draw_fps_counter(frame: np.ndarray, fps: float, position: Tuple[int, int] = (10, 30)) -> np.ndarray:
    """
    Draw FPS counter on frame
    
    Args:
        frame: Input frame
        fps: FPS value to display
        position: Position to draw the counter (x, y)
        
    Returns:
        Frame with FPS counter
    """
    frame_copy = frame.copy()
    
    # Format FPS text
    fps_text = f"FPS: {fps:.1f}"
    
    # Choose color based on FPS
    if fps >= 25:
        color = (0, 255, 0)  # Green
    elif fps >= 15:
        color = (0, 255, 255)  # Yellow
    else:
        color = (0, 0, 255)  # Red
    
    # Draw background rectangle
    text_size = cv2.getTextSize(fps_text, cv2.FONT_HERSHEY_SIMPLEX, 0.7, 2)[0]
    cv2.rectangle(
        frame_copy,
        (position[0] - 5, position[1] - text_size[1] - 5),
        (position[0] + text_size[0] + 5, position[1] + 5),
        (0, 0, 0),
        -1
    )
    
    # Draw FPS text
    cv2.putText(
        frame_copy,
        fps_text,
        position,
        cv2.FONT_HERSHEY_SIMPLEX,
        0.7,
        color,
        2
    )
    
    return frame_copy

def create_confidence_bar(confidence: float, width: int = 200, height: int = 20) -> np.ndarray:
    """
    Create a confidence bar visualization
    
    Args:
        confidence: Confidence value (0.0 to 1.0)
        width: Bar width in pixels
        height: Bar height in pixels
        
    Returns:
        Confidence bar as numpy array
    """
    # Create bar background
    bar = np.zeros((height, width, 3), dtype=np.uint8)
    bar.fill(50)  # Dark gray background
    
    # Calculate fill width
    fill_width = int(width * confidence)
    
    # Choose color based on confidence
    if confidence >= 0.8:
        color = (0, 255, 0)  # Green
    elif confidence >= 0.6:
        color = (0, 255, 255)  # Yellow
    elif confidence >= 0.4:
        color = (0, 165, 255)  # Orange
    else:
        color = (0, 0, 255)  # Red
    
    # Fill the bar
    if fill_width > 0:
        bar[:, :fill_width] = color
    
    # Add border
    cv2.rectangle(bar, (0, 0), (width-1, height-1), (255, 255, 255), 1)
    
    return bar

def validate_model_file(model_path: Path) -> bool:
    """
    Validate that the model file exists and is accessible
    
    Args:
        model_path: Path to the model file
        
    Returns:
        True if model file is valid, False otherwise
    """
    try:
        if not model_path.exists():
            logger.error(f"Model file not found: {model_path}")
            return False
        
        if not model_path.is_file():
            logger.error(f"Model path is not a file: {model_path}")
            return False
        
        if model_path.suffix.lower() != '.pt':
            logger.warning(f"Model file does not have .pt extension: {model_path}")
        
        # Check file size (should be > 1MB for a real model)
        file_size = model_path.stat().st_size
        if file_size < 1024 * 1024:  # 1MB
            logger.warning(f"Model file seems too small: {file_size} bytes")
        
        return True
        
    except Exception as e:
        logger.error(f"Error validating model file: {e}")
        return False

def get_system_info() -> Dict[str, Any]:
    """
    Get system information for debugging
    
    Returns:
        Dictionary with system information
    """
    import platform
    import psutil
    
    try:
        info = {
            "platform": platform.platform(),
            "python_version": platform.python_version(),
            "cpu_count": psutil.cpu_count(),
            "memory_total": psutil.virtual_memory().total,
            "memory_available": psutil.virtual_memory().available,
            "disk_usage": psutil.disk_usage('/').percent if platform.system() != 'Windows' else psutil.disk_usage('C:').percent
        }
        
        # Add OpenCV info
        info["opencv_version"] = cv2.__version__
        
        # Add GPU info if available
        try:
            import torch
            info["torch_version"] = torch.__version__
            info["cuda_available"] = torch.cuda.is_available()
            if torch.cuda.is_available():
                info["cuda_device_count"] = torch.cuda.device_count()
                info["cuda_device_name"] = torch.cuda.get_device_name(0)
        except ImportError:
            info["torch_available"] = False
        
        return info
        
    except Exception as e:
        logger.error(f"Error getting system info: {e}")
        return {"error": str(e)}

def create_detection_overlay(frame: np.ndarray, detections: List[Dict], alpha: float = 0.3) -> np.ndarray:
    """
    Create a semi-transparent overlay for detections
    
    Args:
        frame: Input frame
        detections: List of detection dictionaries
        alpha: Overlay transparency (0.0 to 1.0)
        
    Returns:
        Frame with detection overlay
    """
    overlay = frame.copy()
    
    for detection in detections:
        bbox = detection.get("bbox", (0, 0, 0, 0))
        confidence = detection.get("confidence", 0.0)
        class_name = detection.get("class_name", "unknown")
        
        x1, y1, x2, y2 = bbox
        
        # Choose color based on confidence
        if confidence >= 0.8:
            color = (0, 255, 0)  # Green
        elif confidence >= 0.6:
            color = (0, 255, 255)  # Yellow
        else:
            color = (0, 165, 255)  # Orange
        
        # Draw filled rectangle
        cv2.rectangle(overlay, (x1, y1), (x2, y2), color, -1)
    
    # Blend overlay with original frame
    result = cv2.addWeighted(frame, 1 - alpha, overlay, alpha, 0)
    
    return result

def log_performance_metrics(metrics: Dict[str, Any], log_level: int = logging.INFO):
    """
    Log performance metrics in a formatted way
    
    Args:
        metrics: Dictionary of performance metrics
        log_level: Logging level
    """
    logger.log(log_level, "=== Performance Metrics ===")
    for key, value in metrics.items():
        if isinstance(value, float):
            logger.log(log_level, f"{key}: {value:.3f}")
        else:
            logger.log(log_level, f"{key}: {value}")
    logger.log(log_level, "=" * 30)

@st.cache_data
def load_cached_config() -> Dict[str, Any]:
    """
    Load configuration with caching for better performance
    
    Returns:
        Configuration dictionary
    """
    from config.settings import (
        MODEL_CONFIG, CAMERA_CONFIG, DETECTION_CONFIG,
        UI_CONFIG, SIBI_CLASSES
    )
    
    return {
        "model": MODEL_CONFIG,
        "camera": CAMERA_CONFIG,
        "detection": DETECTION_CONFIG,
        "ui": UI_CONFIG,
        "classes": SIBI_CLASSES
    }

def safe_streamlit_rerun(delay: float = 0.1):
    """
    Safely trigger Streamlit rerun with delay
    
    Args:
        delay: Delay before rerun in seconds
    """
    try:
        time.sleep(delay)
        st.rerun()
    except Exception as e:
        logger.warning(f"Failed to trigger rerun: {e}")

def format_detection_summary(detections: List[Dict]) -> str:
    """
    Format detection results into a readable summary
    
    Args:
        detections: List of detection dictionaries
        
    Returns:
        Formatted summary string
    """
    if not detections:
        return "No detections"
    
    summary_parts = []
    for detection in detections:
        class_name = detection.get("class_name", "unknown")
        confidence = detection.get("confidence", 0.0)
        summary_parts.append(f"{class_name} ({confidence:.2f})")
    
    return ", ".join(summary_parts)
