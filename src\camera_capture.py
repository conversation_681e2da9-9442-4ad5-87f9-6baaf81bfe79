"""
Camera Capture Module for SIBI Sign Language Detection
"""

import cv2
import numpy as np
import threading
import time
import logging
from typing import Op<PERSON>, Tu<PERSON>, Callable
from queue import Queue, Empty
import streamlit as st

from config.settings import CAMERA_CONFIG, PERFORMANCE_CONFIG

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CameraCapture:
    """
    Camera capture class with threading support for real-time video processing
    """
    
    def __init__(self, camera_index: int = 0, width: int = 640, height: int = 480):
        """
        Initialize camera capture
        
        Args:
            camera_index: Camera device index (usually 0 for default camera)
            width: Frame width
            height: Frame height
        """
        self.camera_index = camera_index
        self.width = width
        self.height = height
        self.fps = CAMERA_CONFIG["fps"]
        self.flip_horizontal = CAMERA_CONFIG["flip_horizontal"]
        
        # Camera and threading
        self.cap = None
        self.is_running = False
        self.capture_thread = None
        self.frame_queue = Queue(maxsize=PERFORMANCE_CONFIG["buffer_size"])
        self.current_frame = None
        self.frame_lock = threading.Lock()
        
        # Performance tracking
        self.frame_count = 0
        self.start_time = None
        self.last_fps_update = 0
        self.current_fps = 0
        
        # Frame processing
        self.frame_skip = PERFORMANCE_CONFIG["frame_skip"]
        self.frame_counter = 0
        
        logger.info(f"Initialized camera capture: {width}x{height} @ {self.fps} FPS")
    
    def initialize_camera(self) -> bool:
        """
        Initialize the camera device
        
        Returns:
            bool: True if camera initialized successfully, False otherwise
        """
        try:
            # Release any existing camera
            if self.cap is not None:
                self.cap.release()
            
            # Initialize camera
            self.cap = cv2.VideoCapture(self.camera_index)
            
            if not self.cap.isOpened():
                logger.error(f"Failed to open camera {self.camera_index}")
                return False
            
            # Set camera properties
            self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, self.width)
            self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, self.height)
            self.cap.set(cv2.CAP_PROP_FPS, self.fps)
            
            # Try to enable GPU acceleration if available
            try:
                # Set buffer size to reduce latency
                self.cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
            except Exception as e:
                logger.warning(f"Could not set buffer size: {e}")
            
            # Verify camera settings
            actual_width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            actual_height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            actual_fps = self.cap.get(cv2.CAP_PROP_FPS)
            
            logger.info(f"Camera initialized: {actual_width}x{actual_height} @ {actual_fps} FPS")
            
            # Test frame capture
            ret, frame = self.cap.read()
            if not ret or frame is None:
                logger.error("Failed to capture test frame")
                return False
            
            logger.info("Camera test successful")
            return True
            
        except Exception as e:
            logger.error(f"Camera initialization failed: {e}")
            return False
    
    def start_capture(self) -> bool:
        """
        Start the camera capture thread
        
        Returns:
            bool: True if capture started successfully, False otherwise
        """
        if self.is_running:
            logger.warning("Camera capture already running")
            return True
        
        if not self.initialize_camera():
            return False
        
        try:
            self.is_running = True
            self.start_time = time.time()
            self.capture_thread = threading.Thread(target=self._capture_loop, daemon=True)
            self.capture_thread.start()
            
            logger.info("Camera capture started")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start capture thread: {e}")
            self.is_running = False
            return False
    
    def stop_capture(self):
        """Stop the camera capture thread"""
        if not self.is_running:
            return
        
        logger.info("Stopping camera capture...")
        self.is_running = False
        
        # Wait for thread to finish
        if self.capture_thread and self.capture_thread.is_alive():
            self.capture_thread.join(timeout=2.0)
        
        # Release camera
        if self.cap:
            self.cap.release()
            self.cap = None
        
        # Clear frame queue
        while not self.frame_queue.empty():
            try:
                self.frame_queue.get_nowait()
            except Empty:
                break
        
        logger.info("Camera capture stopped")
    
    def _capture_loop(self):
        """Main capture loop running in separate thread"""
        logger.info("Camera capture loop started")
        
        while self.is_running:
            try:
                ret, frame = self.cap.read()
                
                if not ret or frame is None:
                    logger.warning("Failed to capture frame")
                    time.sleep(0.01)
                    continue
                
                # Apply frame skipping for performance
                self.frame_counter += 1
                if self.frame_counter % self.frame_skip != 0:
                    continue
                
                # Flip frame horizontally if enabled (mirror effect)
                if self.flip_horizontal:
                    frame = cv2.flip(frame, 1)
                
                # Resize frame if needed
                if frame.shape[1] != self.width or frame.shape[0] != self.height:
                    frame = cv2.resize(frame, (self.width, self.height))
                
                # Update current frame with thread safety
                with self.frame_lock:
                    self.current_frame = frame.copy()
                
                # Add frame to queue (non-blocking)
                try:
                    self.frame_queue.put_nowait(frame)
                except:
                    # Queue is full, remove oldest frame and add new one
                    try:
                        self.frame_queue.get_nowait()
                        self.frame_queue.put_nowait(frame)
                    except Empty:
                        pass
                
                # Update performance metrics
                self.frame_count += 1
                current_time = time.time()
                if current_time - self.last_fps_update >= 1.0:
                    elapsed = current_time - self.start_time
                    self.current_fps = self.frame_count / elapsed if elapsed > 0 else 0
                    self.last_fps_update = current_time
                
                # Small delay to prevent excessive CPU usage
                time.sleep(0.001)
                
            except Exception as e:
                logger.error(f"Error in capture loop: {e}")
                time.sleep(0.1)
        
        logger.info("Camera capture loop ended")
    
    def get_frame(self) -> Optional[np.ndarray]:
        """
        Get the latest frame
        
        Returns:
            numpy.ndarray: Latest frame or None if no frame available
        """
        with self.frame_lock:
            return self.current_frame.copy() if self.current_frame is not None else None
    
    def get_frame_from_queue(self) -> Optional[np.ndarray]:
        """
        Get frame from the processing queue
        
        Returns:
            numpy.ndarray: Frame from queue or None if queue is empty
        """
        try:
            return self.frame_queue.get_nowait()
        except Empty:
            return None
    
    def get_fps(self) -> float:
        """
        Get current FPS
        
        Returns:
            float: Current frames per second
        """
        return self.current_fps
    
    def get_camera_info(self) -> dict:
        """
        Get camera information
        
        Returns:
            dict: Camera information and status
        """
        info = {
            "camera_index": self.camera_index,
            "width": self.width,
            "height": self.height,
            "target_fps": self.fps,
            "current_fps": self.current_fps,
            "is_running": self.is_running,
            "frame_count": self.frame_count,
            "flip_horizontal": self.flip_horizontal,
            "frame_skip": self.frame_skip,
        }
        
        if self.cap and self.cap.isOpened():
            try:
                info.update({
                    "actual_width": int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH)),
                    "actual_height": int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT)),
                    "actual_fps": self.cap.get(cv2.CAP_PROP_FPS),
                    "backend": self.cap.getBackendName(),
                })
            except Exception as e:
                logger.warning(f"Could not get camera properties: {e}")
        
        return info
    
    def is_camera_available(self) -> bool:
        """
        Check if camera is available and working
        
        Returns:
            bool: True if camera is available, False otherwise
        """
        return self.cap is not None and self.cap.isOpened() and self.is_running
    
    def __del__(self):
        """Cleanup when object is destroyed"""
        self.stop_capture()

def get_available_cameras() -> list:
    """
    Get list of available camera indices
    
    Returns:
        list: List of available camera indices
    """
    available_cameras = []
    
    # Test camera indices 0-5
    for i in range(6):
        cap = cv2.VideoCapture(i)
        if cap.isOpened():
            ret, _ = cap.read()
            if ret:
                available_cameras.append(i)
        cap.release()
    
    return available_cameras

def test_camera(camera_index: int = 0) -> bool:
    """
    Test if a specific camera works
    
    Args:
        camera_index: Camera index to test
        
    Returns:
        bool: True if camera works, False otherwise
    """
    try:
        cap = cv2.VideoCapture(camera_index)
        if not cap.isOpened():
            return False
        
        ret, frame = cap.read()
        cap.release()
        
        return ret and frame is not None
        
    except Exception:
        return False
