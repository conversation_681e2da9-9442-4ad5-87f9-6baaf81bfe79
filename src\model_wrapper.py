"""
YOLOv11 Model Wrapper for SIBI Sign Language Detection
"""

import torch
import numpy as np
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
import logging
from ultralytics import YOLO
import cv2

from config.settings import (
    MODEL_CONFIG, 
    SIBI_CLASSES, 
    get_optimal_device,
    get_device_info
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SIBIModelWrapper:
    """
    Wrapper class for YOLOv11 SIBI sign language detection model
    """
    
    def __init__(self, model_path: Path, device: Optional[str] = None):
        """
        Initialize the SIBI model wrapper
        
        Args:
            model_path: Path to the YOLOv11 model file (.pt)
            device: Device to run inference on ('cpu', 'cuda', 'mps', or 'auto')
        """
        self.model_path = Path(model_path)
        self.device = device or get_optimal_device()
        self.model = None
        self.is_loaded = False
        self.classes = SIBI_CLASSES
        
        # Model configuration
        self.confidence_threshold = MODEL_CONFIG["confidence_threshold"]
        self.iou_threshold = MODEL_CONFIG["iou_threshold"]
        self.max_detections = MODEL_CONFIG["max_detections"]
        self.half_precision = MODEL_CONFIG["half_precision"]
        
        # Performance tracking
        self.inference_times = []
        self.total_inferences = 0
        
        logger.info(f"Initializing SIBI model wrapper with device: {self.device}")
        self._log_device_info()
        
    def _log_device_info(self):
        """Log information about available compute devices"""
        device_info = get_device_info()
        logger.info(f"Device availability: {device_info}")
        
        if self.device == "cuda" and device_info["cuda_available"]:
            logger.info(f"CUDA device: {device_info['cuda_device_name']}")
            logger.info(f"CUDA memory: {device_info['cuda_memory'] / 1e9:.1f} GB")
    
    def load_model(self) -> bool:
        """
        Load the YOLOv11 model
        
        Returns:
            bool: True if model loaded successfully, False otherwise
        """
        try:
            if not self.model_path.exists():
                logger.error(f"Model file not found: {self.model_path}")
                return False
            
            logger.info(f"Loading model from: {self.model_path}")
            
            # Load YOLO model
            self.model = YOLO(str(self.model_path))
            
            # Move model to specified device
            if hasattr(self.model.model, 'to'):
                self.model.model.to(self.device)
            
            # Enable half precision if supported and requested
            if self.half_precision and self.device != "cpu":
                try:
                    if hasattr(self.model.model, 'half'):
                        self.model.model.half()
                        logger.info("Enabled half precision (FP16)")
                except Exception as e:
                    logger.warning(f"Could not enable half precision: {e}")
            
            # Warm up the model with a dummy inference
            self._warmup_model()
            
            self.is_loaded = True
            logger.info("Model loaded successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to load model: {e}")
            return False
    
    def _warmup_model(self):
        """Warm up the model with a dummy inference"""
        try:
            dummy_image = np.zeros((640, 640, 3), dtype=np.uint8)
            _ = self.model(dummy_image, verbose=False)
            logger.info("Model warmed up successfully")
        except Exception as e:
            logger.warning(f"Model warmup failed: {e}")
    
    def predict(self, image: np.ndarray) -> List[Dict[str, Any]]:
        """
        Run inference on an image
        
        Args:
            image: Input image as numpy array (BGR format)
            
        Returns:
            List of detection dictionaries with keys:
            - class_id: Integer class ID
            - class_name: String class name
            - confidence: Float confidence score
            - bbox: Tuple of (x1, y1, x2, y2) bounding box coordinates
        """
        if not self.is_loaded:
            logger.error("Model not loaded. Call load_model() first.")
            return []
        
        try:
            # Record start time for performance tracking
            import time
            start_time = time.time()
            
            # Run inference
            results = self.model(
                image,
                conf=self.confidence_threshold,
                iou=self.iou_threshold,
                max_det=self.max_detections,
                verbose=False
            )
            
            # Record inference time
            inference_time = time.time() - start_time
            self.inference_times.append(inference_time)
            self.total_inferences += 1
            
            # Keep only last 100 inference times for rolling average
            if len(self.inference_times) > 100:
                self.inference_times = self.inference_times[-100:]
            
            # Parse results
            detections = []
            if results and len(results) > 0:
                result = results[0]  # Get first result
                
                if result.boxes is not None and len(result.boxes) > 0:
                    boxes = result.boxes.xyxy.cpu().numpy()  # Bounding boxes
                    confidences = result.boxes.conf.cpu().numpy()  # Confidence scores
                    class_ids = result.boxes.cls.cpu().numpy().astype(int)  # Class IDs
                    
                    for i in range(len(boxes)):
                        class_id = class_ids[i]
                        confidence = float(confidences[i])
                        bbox = tuple(boxes[i].astype(int))
                        
                        # Get class name from mapping
                        class_name = self.classes.get(class_id, f"unknown_{class_id}")
                        
                        detection = {
                            "class_id": class_id,
                            "class_name": class_name,
                            "confidence": confidence,
                            "bbox": bbox,  # (x1, y1, x2, y2)
                        }
                        detections.append(detection)
            
            return detections
            
        except Exception as e:
            logger.error(f"Inference failed: {e}")
            return []
    
    def get_performance_stats(self) -> Dict[str, float]:
        """
        Get performance statistics
        
        Returns:
            Dictionary with performance metrics
        """
        if not self.inference_times:
            return {
                "avg_inference_time": 0.0,
                "fps": 0.0,
                "total_inferences": self.total_inferences
            }
        
        avg_time = np.mean(self.inference_times)
        fps = 1.0 / avg_time if avg_time > 0 else 0.0
        
        return {
            "avg_inference_time": avg_time,
            "fps": fps,
            "total_inferences": self.total_inferences,
            "min_inference_time": np.min(self.inference_times),
            "max_inference_time": np.max(self.inference_times)
        }
    
    def update_config(self, **kwargs):
        """
        Update model configuration parameters
        
        Args:
            **kwargs: Configuration parameters to update
        """
        if "confidence_threshold" in kwargs:
            self.confidence_threshold = kwargs["confidence_threshold"]
        if "iou_threshold" in kwargs:
            self.iou_threshold = kwargs["iou_threshold"]
        if "max_detections" in kwargs:
            self.max_detections = kwargs["max_detections"]
        
        logger.info(f"Updated model config: conf={self.confidence_threshold}, "
                   f"iou={self.iou_threshold}, max_det={self.max_detections}")
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        Get information about the loaded model
        
        Returns:
            Dictionary with model information
        """
        if not self.is_loaded:
            return {"status": "not_loaded"}
        
        try:
            model_info = {
                "status": "loaded",
                "model_path": str(self.model_path),
                "device": self.device,
                "classes": self.classes,
                "num_classes": len(self.classes),
                "confidence_threshold": self.confidence_threshold,
                "iou_threshold": self.iou_threshold,
                "max_detections": self.max_detections,
                "half_precision": self.half_precision,
            }
            
            # Add model-specific info if available
            if hasattr(self.model, 'model'):
                if hasattr(self.model.model, 'yaml'):
                    model_info["model_yaml"] = self.model.model.yaml
                if hasattr(self.model.model, 'names'):
                    model_info["model_names"] = self.model.model.names
            
            return model_info
            
        except Exception as e:
            logger.error(f"Failed to get model info: {e}")
            return {"status": "error", "error": str(e)}
    
    def __del__(self):
        """Cleanup when object is destroyed"""
        if hasattr(self, 'model') and self.model is not None:
            del self.model
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
