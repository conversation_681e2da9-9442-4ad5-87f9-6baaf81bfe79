# SIBI Sign Language Detection Web Application

A real-time Indonesian Sign Language (SIBI) detection application built with Streamlit and YOLOv11. This application helps deaf tourists communicate by converting their sign language gestures into readable text.

## Features

- **Real-time Camera Input**: Captures hand gestures through your webcam
- **YOLOv11 Model Integration**: Uses a trained model to detect 9 SIBI words
- **Sentence Composition**: Accumulates detected signs into meaningful sentences
- **GPU Acceleration**: Optimized for GPU inference when available
- **User-friendly Interface**: Clean Streamlit interface for easy interaction

## Supported SIBI Words

The application can detect the following Indonesian sign language words:
- mau (want)
- saya (I/me)
- mana (where)
- makan (eat)
- kamu (you)
- jalan (walk/road)
- hotel (hotel)
- ke (to)
- di (at/in)

## Installation

1. Clone this repository
2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```
3. Run the application:
   ```bash
   streamlit run app.py
   ```

## Usage

1. Launch the application
2. Allow camera access when prompted
3. Perform SIBI gestures in front of the camera
4. Watch as detected words appear and compose into sentences
5. Use the interface controls to manage your session

## Technical Requirements

- Python 3.8+
- Webcam/Camera access
- GPU recommended for optimal performance
- Windows/Linux/macOS compatible

## Model Information

- **Model Type**: YOLOv11
- **Format**: PyTorch (.pt)
- **Classes**: 9 SIBI words
- **Location**: `models/sibiv3.pt`
