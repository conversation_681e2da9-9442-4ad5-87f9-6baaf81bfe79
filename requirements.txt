# Core dependencies for SIBI Sign Language Detection App
streamlit>=1.28.0
ultralytics>=8.0.0
opencv-python>=4.8.0
torch>=2.0.0
torchvision>=0.15.0
numpy>=1.24.0
pillow>=9.5.0
pandas>=2.0.0

# Additional utilities
python-dotenv>=1.0.0
pydantic>=2.0.0
typing-extensions>=4.5.0

# For GPU acceleration (optional, will install CPU version if GPU not available)
# torch[cuda] - uncomment if you want to force CUDA installation
