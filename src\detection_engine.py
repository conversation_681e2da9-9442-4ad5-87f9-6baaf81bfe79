"""
Sign Detection Engine for SIBI Sign Language Detection
"""

import cv2
import numpy as np
import time
import logging
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from collections import defaultdict, deque

from src.model_wrapper import SIBIModelWrapper
from config.settings import DETECTION_CONFIG, MODEL_CONFIG, SIBI_CLASSES

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class Detection:
    """Data class for a single detection"""
    class_id: int
    class_name: str
    confidence: float
    bbox: Tuple[int, int, int, int]  # (x1, y1, x2, y2)
    timestamp: float
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert detection to dictionary"""
        return {
            "class_id": self.class_id,
            "class_name": self.class_name,
            "confidence": self.confidence,
            "bbox": self.bbox,
            "timestamp": self.timestamp
        }

@dataclass
class DetectionResult:
    """Data class for detection results from a frame"""
    detections: List[Detection]
    frame_timestamp: float
    processing_time: float
    frame_shape: Tuple[int, int, int]
    
    def get_best_detection(self) -> Optional[Detection]:
        """Get detection with highest confidence"""
        if not self.detections:
            return None
        return max(self.detections, key=lambda d: d.confidence)
    
    def get_detections_by_class(self, class_name: str) -> List[Detection]:
        """Get all detections for a specific class"""
        return [d for d in self.detections if d.class_name == class_name]

class SignDetectionEngine:
    """
    Core detection engine that processes video frames and detects SIBI signs
    """
    
    def __init__(self, model_wrapper: SIBIModelWrapper):
        """
        Initialize the detection engine
        
        Args:
            model_wrapper: Initialized SIBI model wrapper
        """
        self.model = model_wrapper
        self.min_confidence = DETECTION_CONFIG["min_confidence"]
        self.detection_cooldown = DETECTION_CONFIG["detection_cooldown"]
        
        # Detection tracking
        self.last_detections = {}  # class_name -> timestamp
        self.detection_history = deque(maxlen=100)  # Keep last 100 detections
        self.class_counts = defaultdict(int)
        
        # Performance tracking
        self.total_frames_processed = 0
        self.total_processing_time = 0
        self.detection_stats = defaultdict(list)
        
        logger.info("Sign detection engine initialized")
    
    def process_frame(self, frame: np.ndarray) -> DetectionResult:
        """
        Process a single frame and detect signs
        
        Args:
            frame: Input frame as numpy array (BGR format)
            
        Returns:
            DetectionResult: Detection results for the frame
        """
        start_time = time.time()
        frame_timestamp = start_time
        
        try:
            # Run model inference
            raw_detections = self.model.predict(frame)
            
            # Filter and process detections
            filtered_detections = self._filter_detections(raw_detections, frame_timestamp)
            
            # Create detection objects
            detections = []
            for det in filtered_detections:
                detection = Detection(
                    class_id=det["class_id"],
                    class_name=det["class_name"],
                    confidence=det["confidence"],
                    bbox=det["bbox"],
                    timestamp=frame_timestamp
                )
                detections.append(detection)
            
            # Update statistics
            processing_time = time.time() - start_time
            self.total_frames_processed += 1
            self.total_processing_time += processing_time
            
            # Update detection history
            if detections:
                self.detection_history.append({
                    "timestamp": frame_timestamp,
                    "detections": [d.to_dict() for d in detections],
                    "processing_time": processing_time
                })
            
            # Update class counts
            for detection in detections:
                self.class_counts[detection.class_name] += 1
                self.detection_stats[detection.class_name].append(detection.confidence)
            
            return DetectionResult(
                detections=detections,
                frame_timestamp=frame_timestamp,
                processing_time=processing_time,
                frame_shape=frame.shape
            )
            
        except Exception as e:
            logger.error(f"Error processing frame: {e}")
            processing_time = time.time() - start_time
            return DetectionResult(
                detections=[],
                frame_timestamp=frame_timestamp,
                processing_time=processing_time,
                frame_shape=frame.shape
            )
    
    def _filter_detections(self, raw_detections: List[Dict], timestamp: float) -> List[Dict]:
        """
        Filter detections based on confidence and cooldown
        
        Args:
            raw_detections: Raw detections from model
            timestamp: Current timestamp
            
        Returns:
            List of filtered detections
        """
        filtered = []
        
        for detection in raw_detections:
            class_name = detection["class_name"]
            confidence = detection["confidence"]
            
            # Check minimum confidence
            if confidence < self.min_confidence:
                continue
            
            # Check cooldown period
            if class_name in self.last_detections:
                time_since_last = timestamp - self.last_detections[class_name]
                if time_since_last < self.detection_cooldown:
                    continue
            
            # Update last detection time
            self.last_detections[class_name] = timestamp
            filtered.append(detection)
        
        return filtered
    
    def draw_detections(self, frame: np.ndarray, detections: List[Detection]) -> np.ndarray:
        """
        Draw detection bounding boxes and labels on frame
        
        Args:
            frame: Input frame
            detections: List of detections to draw
            
        Returns:
            Frame with drawn detections
        """
        frame_copy = frame.copy()
        
        for detection in detections:
            x1, y1, x2, y2 = detection.bbox
            class_name = detection.class_name
            confidence = detection.confidence
            
            # Choose color based on confidence
            if confidence > 0.8:
                color = (0, 255, 0)  # Green for high confidence
            elif confidence > 0.6:
                color = (0, 255, 255)  # Yellow for medium confidence
            else:
                color = (0, 165, 255)  # Orange for low confidence
            
            # Draw bounding box
            cv2.rectangle(frame_copy, (x1, y1), (x2, y2), color, 2)
            
            # Draw label background
            label = f"{class_name}: {confidence:.2f}"
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
            cv2.rectangle(
                frame_copy,
                (x1, y1 - label_size[1] - 10),
                (x1 + label_size[0], y1),
                color,
                -1
            )
            
            # Draw label text
            cv2.putText(
                frame_copy,
                label,
                (x1, y1 - 5),
                cv2.FONT_HERSHEY_SIMPLEX,
                0.6,
                (255, 255, 255),
                2
            )
        
        return frame_copy
    
    def get_detection_statistics(self) -> Dict[str, Any]:
        """
        Get detection statistics
        
        Returns:
            Dictionary with detection statistics
        """
        avg_processing_time = (
            self.total_processing_time / self.total_frames_processed
            if self.total_frames_processed > 0 else 0
        )
        
        fps = 1.0 / avg_processing_time if avg_processing_time > 0 else 0
        
        # Calculate per-class statistics
        class_stats = {}
        for class_name, confidences in self.detection_stats.items():
            if confidences:
                class_stats[class_name] = {
                    "count": len(confidences),
                    "avg_confidence": np.mean(confidences),
                    "max_confidence": np.max(confidences),
                    "min_confidence": np.min(confidences)
                }
        
        return {
            "total_frames_processed": self.total_frames_processed,
            "total_processing_time": self.total_processing_time,
            "avg_processing_time": avg_processing_time,
            "estimated_fps": fps,
            "total_detections": sum(self.class_counts.values()),
            "class_counts": dict(self.class_counts),
            "class_statistics": class_stats,
            "recent_detections": len(self.detection_history),
            "model_performance": self.model.get_performance_stats()
        }
    
    def get_recent_detections(self, seconds: float = 5.0) -> List[Dict]:
        """
        Get recent detections within specified time window
        
        Args:
            seconds: Time window in seconds
            
        Returns:
            List of recent detections
        """
        current_time = time.time()
        cutoff_time = current_time - seconds
        
        recent = []
        for entry in reversed(self.detection_history):
            if entry["timestamp"] >= cutoff_time:
                recent.append(entry)
            else:
                break
        
        return list(reversed(recent))
    
    def reset_statistics(self):
        """Reset all statistics and counters"""
        self.total_frames_processed = 0
        self.total_processing_time = 0
        self.class_counts.clear()
        self.detection_stats.clear()
        self.detection_history.clear()
        self.last_detections.clear()
        
        logger.info("Detection statistics reset")
    
    def update_config(self, **kwargs):
        """
        Update detection configuration
        
        Args:
            **kwargs: Configuration parameters to update
        """
        if "min_confidence" in kwargs:
            self.min_confidence = kwargs["min_confidence"]
        if "detection_cooldown" in kwargs:
            self.detection_cooldown = kwargs["detection_cooldown"]
        
        # Update model config if provided
        model_params = {k: v for k, v in kwargs.items() 
                       if k in ["confidence_threshold", "iou_threshold", "max_detections"]}
        if model_params:
            self.model.update_config(**model_params)
        
        logger.info(f"Updated detection config: min_conf={self.min_confidence}, "
                   f"cooldown={self.detection_cooldown}")
    
    def get_config(self) -> Dict[str, Any]:
        """
        Get current detection configuration
        
        Returns:
            Dictionary with current configuration
        """
        return {
            "min_confidence": self.min_confidence,
            "detection_cooldown": self.detection_cooldown,
            "model_config": self.model.get_model_info(),
            "supported_classes": SIBI_CLASSES
        }
