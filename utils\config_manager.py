"""
Configuration Manager for SIBI Sign Language Detection Application
"""

import json
import logging
from pathlib import Path
from typing import Dict, Any, Optional
import streamlit as st

from config.settings import (
    MODEL_CONFIG, CAMERA_CONFIG, DETECTION_CONFIG, 
    UI_CONFIG, PERFORMANCE_CONFIG
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ConfigManager:
    """
    Manages application configuration with persistence and validation
    """
    
    def __init__(self, config_file: Optional[Path] = None):
        """
        Initialize configuration manager
        
        Args:
            config_file: Path to configuration file (optional)
        """
        self.config_file = config_file or Path("config/user_settings.json")
        self.config_file.parent.mkdir(exist_ok=True)
        
        # Default configuration
        self.default_config = {
            "model": MODEL_CONFIG.copy(),
            "camera": CAMERA_CONFIG.copy(),
            "detection": DETECTION_CONFIG.copy(),
            "ui": UI_CONFIG.copy(),
            "performance": PERFORMANCE_CONFIG.copy()
        }
        
        # Current configuration
        self.current_config = self.default_config.copy()
        
        # Load saved configuration
        self.load_config()
    
    def load_config(self) -> bool:
        """
        Load configuration from file
        
        Returns:
            True if loaded successfully, False otherwise
        """
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r') as f:
                    saved_config = json.load(f)
                
                # Merge with default config to handle new settings
                self._merge_config(saved_config)
                logger.info(f"Configuration loaded from {self.config_file}")
                return True
            else:
                logger.info("No saved configuration found, using defaults")
                return False
                
        except Exception as e:
            logger.error(f"Failed to load configuration: {e}")
            return False
    
    def save_config(self) -> bool:
        """
        Save current configuration to file
        
        Returns:
            True if saved successfully, False otherwise
        """
        try:
            with open(self.config_file, 'w') as f:
                json.dump(self.current_config, f, indent=2)
            
            logger.info(f"Configuration saved to {self.config_file}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to save configuration: {e}")
            return False
    
    def _merge_config(self, saved_config: Dict[str, Any]):
        """
        Merge saved configuration with default configuration
        
        Args:
            saved_config: Configuration loaded from file
        """
        for section, settings in saved_config.items():
            if section in self.current_config:
                if isinstance(settings, dict):
                    self.current_config[section].update(settings)
                else:
                    self.current_config[section] = settings
    
    def get_config(self, section: Optional[str] = None) -> Dict[str, Any]:
        """
        Get configuration section or entire configuration
        
        Args:
            section: Configuration section name (optional)
            
        Returns:
            Configuration dictionary
        """
        if section:
            return self.current_config.get(section, {})
        return self.current_config
    
    def update_config(self, section: str, key: str, value: Any) -> bool:
        """
        Update a specific configuration value
        
        Args:
            section: Configuration section
            key: Configuration key
            value: New value
            
        Returns:
            True if updated successfully, False otherwise
        """
        try:
            if section not in self.current_config:
                self.current_config[section] = {}
            
            self.current_config[section][key] = value
            logger.info(f"Updated config: {section}.{key} = {value}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to update config: {e}")
            return False
    
    def reset_to_defaults(self, section: Optional[str] = None) -> bool:
        """
        Reset configuration to defaults
        
        Args:
            section: Section to reset (optional, resets all if None)
            
        Returns:
            True if reset successfully, False otherwise
        """
        try:
            if section:
                if section in self.default_config:
                    self.current_config[section] = self.default_config[section].copy()
                    logger.info(f"Reset {section} configuration to defaults")
                else:
                    logger.warning(f"Unknown configuration section: {section}")
                    return False
            else:
                self.current_config = self.default_config.copy()
                logger.info("Reset all configuration to defaults")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to reset configuration: {e}")
            return False
    
    def validate_config(self) -> Dict[str, list]:
        """
        Validate current configuration
        
        Returns:
            Dictionary with validation errors by section
        """
        errors = {}
        
        # Validate model config
        model_errors = []
        model_config = self.current_config.get("model", {})
        
        if not (0.0 <= model_config.get("confidence_threshold", 0.5) <= 1.0):
            model_errors.append("confidence_threshold must be between 0.0 and 1.0")
        
        if not (0.0 <= model_config.get("iou_threshold", 0.45) <= 1.0):
            model_errors.append("iou_threshold must be between 0.0 and 1.0")
        
        if model_config.get("max_detections", 10) <= 0:
            model_errors.append("max_detections must be positive")
        
        if model_errors:
            errors["model"] = model_errors
        
        # Validate camera config
        camera_errors = []
        camera_config = self.current_config.get("camera", {})
        
        if camera_config.get("width", 640) <= 0:
            camera_errors.append("width must be positive")
        
        if camera_config.get("height", 480) <= 0:
            camera_errors.append("height must be positive")
        
        if camera_config.get("fps", 30) <= 0:
            camera_errors.append("fps must be positive")
        
        if camera_errors:
            errors["camera"] = camera_errors
        
        # Validate detection config
        detection_errors = []
        detection_config = self.current_config.get("detection", {})
        
        if not (0.0 <= detection_config.get("min_confidence", 0.3) <= 1.0):
            detection_errors.append("min_confidence must be between 0.0 and 1.0")
        
        if detection_config.get("detection_cooldown", 1.0) < 0:
            detection_errors.append("detection_cooldown must be non-negative")
        
        if detection_config.get("max_sentence_length", 20) <= 0:
            detection_errors.append("max_sentence_length must be positive")
        
        if detection_errors:
            errors["detection"] = detection_errors
        
        return errors

def create_settings_ui(config_manager: ConfigManager) -> bool:
    """
    Create Streamlit UI for configuration settings
    
    Args:
        config_manager: Configuration manager instance
        
    Returns:
        True if settings were changed, False otherwise
    """
    settings_changed = False
    
    st.header("⚙️ Application Settings")
    
    # Model Settings
    with st.expander("🤖 Model Settings", expanded=True):
        model_config = config_manager.get_config("model")
        
        new_confidence = st.slider(
            "Detection Confidence Threshold",
            min_value=0.1,
            max_value=1.0,
            value=model_config.get("confidence_threshold", 0.5),
            step=0.05,
            help="Minimum confidence score for detections"
        )
        
        new_iou = st.slider(
            "IoU Threshold",
            min_value=0.1,
            max_value=1.0,
            value=model_config.get("iou_threshold", 0.45),
            step=0.05,
            help="Intersection over Union threshold for non-maximum suppression"
        )
        
        new_max_det = st.number_input(
            "Maximum Detections",
            min_value=1,
            max_value=50,
            value=model_config.get("max_detections", 10),
            help="Maximum number of detections per frame"
        )
        
        new_half_precision = st.checkbox(
            "Use Half Precision (FP16)",
            value=model_config.get("half_precision", True),
            help="Use FP16 for faster inference (requires compatible GPU)"
        )
        
        # Update model config if changed
        if (new_confidence != model_config.get("confidence_threshold") or
            new_iou != model_config.get("iou_threshold") or
            new_max_det != model_config.get("max_detections") or
            new_half_precision != model_config.get("half_precision")):
            
            config_manager.update_config("model", "confidence_threshold", new_confidence)
            config_manager.update_config("model", "iou_threshold", new_iou)
            config_manager.update_config("model", "max_detections", new_max_det)
            config_manager.update_config("model", "half_precision", new_half_precision)
            settings_changed = True
    
    # Camera Settings
    with st.expander("📹 Camera Settings"):
        camera_config = config_manager.get_config("camera")
        
        col1, col2 = st.columns(2)
        with col1:
            new_width = st.number_input(
                "Frame Width",
                min_value=320,
                max_value=1920,
                value=camera_config.get("width", 640),
                step=32
            )
        
        with col2:
            new_height = st.number_input(
                "Frame Height",
                min_value=240,
                max_value=1080,
                value=camera_config.get("height", 480),
                step=24
            )
        
        new_fps = st.number_input(
            "Target FPS",
            min_value=5,
            max_value=60,
            value=camera_config.get("fps", 30)
        )
        
        new_flip = st.checkbox(
            "Flip Horizontally (Mirror)",
            value=camera_config.get("flip_horizontal", True),
            help="Mirror the camera feed horizontally"
        )
        
        # Update camera config if changed
        if (new_width != camera_config.get("width") or
            new_height != camera_config.get("height") or
            new_fps != camera_config.get("fps") or
            new_flip != camera_config.get("flip_horizontal")):
            
            config_manager.update_config("camera", "width", new_width)
            config_manager.update_config("camera", "height", new_height)
            config_manager.update_config("camera", "fps", new_fps)
            config_manager.update_config("camera", "flip_horizontal", new_flip)
            settings_changed = True
    
    # Detection Settings
    with st.expander("🔍 Detection Settings"):
        detection_config = config_manager.get_config("detection")
        
        new_min_conf = st.slider(
            "Minimum Word Confidence",
            min_value=0.1,
            max_value=1.0,
            value=detection_config.get("min_confidence", 0.3),
            step=0.05,
            help="Minimum confidence for word acceptance"
        )
        
        new_cooldown = st.number_input(
            "Detection Cooldown (seconds)",
            min_value=0.1,
            max_value=5.0,
            value=detection_config.get("detection_cooldown", 1.0),
            step=0.1,
            help="Minimum time between same word detections"
        )
        
        new_max_length = st.number_input(
            "Maximum Sentence Length",
            min_value=5,
            max_value=50,
            value=detection_config.get("max_sentence_length", 20),
            help="Maximum number of words in a sentence"
        )
        
        new_timeout = st.number_input(
            "Auto-clear Timeout (seconds)",
            min_value=5.0,
            max_value=120.0,
            value=detection_config.get("auto_clear_timeout", 30.0),
            step=5.0,
            help="Auto-clear sentence after inactivity"
        )
        
        # Update detection config if changed
        if (new_min_conf != detection_config.get("min_confidence") or
            new_cooldown != detection_config.get("detection_cooldown") or
            new_max_length != detection_config.get("max_sentence_length") or
            new_timeout != detection_config.get("auto_clear_timeout")):
            
            config_manager.update_config("detection", "min_confidence", new_min_conf)
            config_manager.update_config("detection", "detection_cooldown", new_cooldown)
            config_manager.update_config("detection", "max_sentence_length", new_max_length)
            config_manager.update_config("detection", "auto_clear_timeout", new_timeout)
            settings_changed = True
    
    # Control buttons
    col1, col2, col3 = st.columns(3)
    
    with col1:
        if st.button("💾 Save Settings", use_container_width=True):
            if config_manager.save_config():
                st.success("Settings saved successfully!")
            else:
                st.error("Failed to save settings")
    
    with col2:
        if st.button("🔄 Reset to Defaults", use_container_width=True):
            if config_manager.reset_to_defaults():
                st.success("Settings reset to defaults!")
                st.rerun()
            else:
                st.error("Failed to reset settings")
    
    with col3:
        if st.button("✅ Validate Settings", use_container_width=True):
            errors = config_manager.validate_config()
            if errors:
                st.error("Configuration validation failed:")
                for section, error_list in errors.items():
                    st.write(f"**{section}:**")
                    for error in error_list:
                        st.write(f"- {error}")
            else:
                st.success("All settings are valid!")
    
    return settings_changed

# Global config manager instance
_config_manager = None

def get_config_manager() -> ConfigManager:
    """Get global configuration manager instance"""
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager()
    return _config_manager
