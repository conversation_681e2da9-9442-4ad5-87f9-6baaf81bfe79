"""
Test script for SIBI Sign Language Detection Application
"""

import sys
import time
import logging
from pathlib import Path
import cv2
import numpy as np

# Add src directory to path
sys.path.append(str(Path(__file__).parent / "src"))

from src.model_wrapper import SIBIModelWrapper
from src.camera_capture import Camera<PERSON>apture, get_available_cameras, test_camera
from src.detection_engine import SignDetectionEngine
from src.sentence_composer import SentenceComposer
from config.settings import MODEL_PATH, get_device_info
from utils.helpers import get_system_info, validate_model_file

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_system_requirements():
    """Test system requirements and dependencies"""
    logger.info("=== Testing System Requirements ===")
    
    try:
        # Test system info
        system_info = get_system_info()
        logger.info(f"System: {system_info.get('platform', 'Unknown')}")
        logger.info(f"Python: {system_info.get('python_version', 'Unknown')}")
        logger.info(f"OpenCV: {system_info.get('opencv_version', 'Unknown')}")
        
        # Test GPU availability
        device_info = get_device_info()
        logger.info(f"CUDA available: {device_info.get('cuda_available', False)}")
        if device_info.get('cuda_available'):
            logger.info(f"CUDA device: {device_info.get('cuda_device_name', 'Unknown')}")
        
        # Test model file
        model_valid = validate_model_file(MODEL_PATH)
        logger.info(f"Model file valid: {model_valid}")
        
        return True
        
    except Exception as e:
        logger.error(f"System requirements test failed: {e}")
        return False

def test_model_loading():
    """Test model loading and basic inference"""
    logger.info("=== Testing Model Loading ===")
    
    try:
        # Initialize model wrapper
        model_wrapper = SIBIModelWrapper(MODEL_PATH)
        
        # Load model
        if not model_wrapper.load_model():
            logger.error("Failed to load model")
            return False
        
        logger.info("Model loaded successfully")
        
        # Test inference with dummy image
        dummy_image = np.zeros((640, 640, 3), dtype=np.uint8)
        detections = model_wrapper.predict(dummy_image)
        
        logger.info(f"Dummy inference completed: {len(detections)} detections")
        
        # Get performance stats
        stats = model_wrapper.get_performance_stats()
        logger.info(f"Model performance: {stats}")
        
        return True
        
    except Exception as e:
        logger.error(f"Model loading test failed: {e}")
        return False

def test_camera_functionality():
    """Test camera capture functionality"""
    logger.info("=== Testing Camera Functionality ===")
    
    try:
        # Get available cameras
        available_cameras = get_available_cameras()
        logger.info(f"Available cameras: {available_cameras}")
        
        if not available_cameras:
            logger.warning("No cameras found")
            return False
        
        # Test first available camera
        camera_index = available_cameras[0]
        logger.info(f"Testing camera {camera_index}")
        
        # Test basic camera access
        if not test_camera(camera_index):
            logger.error(f"Camera {camera_index} test failed")
            return False
        
        # Test camera capture class
        camera = CameraCapture(camera_index=camera_index)
        
        if not camera.start_capture():
            logger.error("Failed to start camera capture")
            return False
        
        # Capture a few frames
        for i in range(5):
            frame = camera.get_frame()
            if frame is not None:
                logger.info(f"Frame {i+1}: {frame.shape}")
            else:
                logger.warning(f"Frame {i+1}: None")
            time.sleep(0.1)
        
        # Get camera info
        camera_info = camera.get_camera_info()
        logger.info(f"Camera info: {camera_info}")
        
        # Stop camera
        camera.stop_capture()
        logger.info("Camera test completed successfully")
        
        return True
        
    except Exception as e:
        logger.error(f"Camera functionality test failed: {e}")
        return False

def test_detection_pipeline():
    """Test the complete detection pipeline"""
    logger.info("=== Testing Detection Pipeline ===")
    
    try:
        # Initialize components
        model_wrapper = SIBIModelWrapper(MODEL_PATH)
        if not model_wrapper.load_model():
            logger.error("Failed to load model for pipeline test")
            return False
        
        detection_engine = SignDetectionEngine(model_wrapper)
        sentence_composer = SentenceComposer()
        
        # Test with dummy frames
        for i in range(3):
            # Create dummy frame with some content
            frame = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
            
            # Process frame
            detection_result = detection_engine.process_frame(frame)
            logger.info(f"Frame {i+1} processed: {len(detection_result.detections)} detections")
            
            # Process for sentence composition
            sentence_composer.process_detection_result(detection_result)
            
            time.sleep(0.1)
        
        # Get statistics
        detection_stats = detection_engine.get_detection_statistics()
        composer_stats = sentence_composer.get_statistics()
        
        logger.info(f"Detection stats: {detection_stats}")
        logger.info(f"Composer stats: {composer_stats}")
        
        return True
        
    except Exception as e:
        logger.error(f"Detection pipeline test failed: {e}")
        return False

def test_performance_benchmark():
    """Run performance benchmark"""
    logger.info("=== Running Performance Benchmark ===")
    
    try:
        # Initialize model
        model_wrapper = SIBIModelWrapper(MODEL_PATH)
        if not model_wrapper.load_model():
            logger.error("Failed to load model for benchmark")
            return False
        
        # Benchmark parameters
        num_frames = 50
        frame_sizes = [(320, 240), (640, 480), (1280, 720)]
        
        for width, height in frame_sizes:
            logger.info(f"Benchmarking {width}x{height} frames...")
            
            total_time = 0
            successful_inferences = 0
            
            for i in range(num_frames):
                # Create random frame
                frame = np.random.randint(0, 255, (height, width, 3), dtype=np.uint8)
                
                # Measure inference time
                start_time = time.time()
                detections = model_wrapper.predict(frame)
                inference_time = time.time() - start_time
                
                total_time += inference_time
                successful_inferences += 1
                
                if i % 10 == 0:
                    logger.info(f"  Frame {i+1}/{num_frames}: {inference_time:.3f}s")
            
            # Calculate statistics
            avg_time = total_time / successful_inferences
            fps = 1.0 / avg_time
            
            logger.info(f"Results for {width}x{height}:")
            logger.info(f"  Average inference time: {avg_time:.3f}s")
            logger.info(f"  Estimated FPS: {fps:.1f}")
            logger.info(f"  Successful inferences: {successful_inferences}/{num_frames}")
        
        return True
        
    except Exception as e:
        logger.error(f"Performance benchmark failed: {e}")
        return False

def test_error_handling():
    """Test error handling and edge cases"""
    logger.info("=== Testing Error Handling ===")
    
    try:
        # Test invalid model path
        try:
            invalid_model = SIBIModelWrapper(Path("nonexistent_model.pt"))
            invalid_model.load_model()
            logger.warning("Invalid model path should have failed")
        except Exception:
            logger.info("Invalid model path handled correctly")
        
        # Test invalid camera index
        try:
            invalid_camera = CameraCapture(camera_index=999)
            invalid_camera.start_capture()
            logger.warning("Invalid camera index should have failed")
        except Exception:
            logger.info("Invalid camera index handled correctly")
        
        # Test empty frame processing
        model_wrapper = SIBIModelWrapper(MODEL_PATH)
        if model_wrapper.load_model():
            detection_engine = SignDetectionEngine(model_wrapper)
            
            # Test with None frame
            try:
                result = detection_engine.process_frame(None)
                logger.info("None frame handled gracefully")
            except Exception as e:
                logger.warning(f"None frame caused error: {e}")
            
            # Test with invalid frame
            try:
                invalid_frame = np.array([])
                result = detection_engine.process_frame(invalid_frame)
                logger.info("Invalid frame handled gracefully")
            except Exception as e:
                logger.warning(f"Invalid frame caused error: {e}")
        
        return True
        
    except Exception as e:
        logger.error(f"Error handling test failed: {e}")
        return False

def run_all_tests():
    """Run all tests"""
    logger.info("Starting SIBI Application Tests...")
    
    tests = [
        ("System Requirements", test_system_requirements),
        ("Model Loading", test_model_loading),
        ("Camera Functionality", test_camera_functionality),
        ("Detection Pipeline", test_detection_pipeline),
        ("Performance Benchmark", test_performance_benchmark),
        ("Error Handling", test_error_handling),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"Running: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            start_time = time.time()
            result = test_func()
            duration = time.time() - start_time
            
            results[test_name] = {
                "passed": result,
                "duration": duration
            }
            
            status = "PASSED" if result else "FAILED"
            logger.info(f"{test_name}: {status} ({duration:.2f}s)")
            
        except Exception as e:
            results[test_name] = {
                "passed": False,
                "duration": 0,
                "error": str(e)
            }
            logger.error(f"{test_name}: FAILED with exception: {e}")
    
    # Print summary
    logger.info(f"\n{'='*50}")
    logger.info("TEST SUMMARY")
    logger.info(f"{'='*50}")
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result["passed"] else "❌ FAILED"
        duration = result["duration"]
        logger.info(f"{test_name}: {status} ({duration:.2f}s)")
        
        if result["passed"]:
            passed += 1
        elif "error" in result:
            logger.info(f"  Error: {result['error']}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! The application is ready to use.")
    else:
        logger.warning("⚠️ Some tests failed. Please check the logs above.")
    
    return passed == total

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
